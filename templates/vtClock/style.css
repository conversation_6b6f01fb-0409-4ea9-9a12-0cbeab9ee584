/* Traditional Broadcast VT Clock Styling */

@import url('https://fonts.googleapis.com/css2?family=Arial:wght@400;700;900&display=swap');

.vtClock-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #000000;
  display: flex;
  font-family: Arial, sans-serif;
  color: white;
  overflow: hidden;
}

/* Left Side - Traditional Countdown Clock */
.vtClock-left {
  width: 50%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  background: #000000;
}

/* Traditional Semicircular Clock */
.vtClock-clockContainer {
  position: relative;
  width: 500px;
  height: 300px;
}

.vtClock-clockFace {
  position: relative;
  width: 500px;
  height: 250px;
  overflow: hidden;
}

/* Semicircular Arc */
.vtClock-arc {
  position: absolute;
  top: 0;
  left: 0;
  width: 500px;
  height: 250px;
}

.vtClock-arcPath {
  fill: none;
  stroke: white;
  stroke-width: 4;
}

/* Clock Hand */
.vtClock-hand {
  position: absolute;
  top: 0;
  left: 0;
  width: 500px;
  height: 250px;
  transform-origin: 250px 250px;
}

.vtClock-handLine {
  stroke: white;
  stroke-width: 3;
  fill: none;
}

/* Center Circle with Time Display */
.vtClock-centerCircle {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 120px;
  height: 120px;
  background: black;
  border: 4px solid white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.vtClock-timeDisplay {
  font-size: 48px;
  font-weight: 900;
  color: white;
  font-family: Arial, sans-serif;
  text-align: center;
}

/* Tick Marks and Numbers */
.vtClock-tickMarks {
  position: absolute;
  top: 0;
  left: 0;
  width: 500px;
  height: 250px;
}

.vtClock-tick {
  stroke: white;
  stroke-width: 2;
  fill: none;
}

.vtClock-tickNumber {
  fill: white;
  font-family: Arial, sans-serif;
  font-size: 24px;
  font-weight: bold;
  text-anchor: middle;
  dominant-baseline: middle;
}

/* Right Side - Traditional Broadcast Text */
.vtClock-right {
  width: 50%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 80px 100px 80px 60px;
  background: #000000;
}

.vtClock-textField {
  margin-bottom: 25px;
  text-align: left;
}

.vtClock-title {
  font-size: 36px;
  font-weight: normal;
  color: white;
  font-family: Arial, sans-serif;
  margin-bottom: 40px;
  letter-spacing: 2px;
}

.vtClock-fieldValue {
  font-size: 28px;
  font-weight: normal;
  color: white;
  font-family: Arial, sans-serif;
  line-height: 1.4;
  margin: 0;
}

.vtClock-fieldValue.bold {
  font-weight: bold;
}

.vtClock-fieldValue.large {
  font-size: 32px;
}

.vtClock-fieldValue.small {
  font-size: 24px;
}

.vtClock-fieldValue.large {
  font-size: 42px;
}

.vtClock-fieldValue.small {
  font-size: 24px;
}

/* Responsive adjustments */
@media (max-width: 1600px) {
  .vtClock-clockContainer {
    width: 350px;
    height: 350px;
  }
  
  .vtClock-timeDisplay {
    font-size: 60px;
  }
  
  .vtClock-progressRing {
    width: 330px;
    height: 330px;
  }
}

@media (max-width: 1200px) {
  .vtClock-left,
  .vtClock-right {
    width: 50%;
  }
  
  .vtClock-right {
    padding: 40px;
  }
  
  .vtClock-fieldValue {
    font-size: 28px;
  }
  
  .vtClock-fieldValue.large {
    font-size: 36px;
  }
}

/* Control Panel (for testing - hidden in production) */
.vtClock-controls {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.1);
  padding: 15px;
  border-radius: 5px;
  display: none; /* Hidden by default for broadcast */
  gap: 10px;
  z-index: 10000;
}

.vtClock-controls.visible {
  display: flex;
}

.vtClock-controlButton {
  padding: 8px 16px;
  background: white;
  color: black;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-weight: bold;
  font-size: 12px;
  font-family: Arial, sans-serif;
}

.vtClock-controlButton:hover {
  background: #f0f0f0;
}

/* Responsive adjustments for broadcast */
@media (max-width: 1600px) {
  .vtClock-clockContainer {
    width: 400px;
    height: 240px;
  }

  .vtClock-clockFace {
    width: 400px;
    height: 200px;
  }

  .vtClock-arc,
  .vtClock-hand,
  .vtClock-tickMarks {
    width: 400px;
    height: 200px;
  }

  .vtClock-hand {
    transform-origin: 200px 200px;
  }
}
