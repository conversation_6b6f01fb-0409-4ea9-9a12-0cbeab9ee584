/* VT Clock Broadcast Interface Styling */

@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Roboto:wght@300;400;700;900&display=swap');

.vtClock-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background:
    radial-gradient(circle at 25% 25%, rgba(52, 152, 219, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(231, 76, 60, 0.1) 0%, transparent 50%),
    linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  display: flex;
  font-family: 'Roboto', 'Arial Black', Arial, sans-serif;
  color: white;
  overflow: hidden;
  box-shadow: inset 0 0 100px rgba(0, 0, 0, 0.3);
}

/* Left Side - Countdown Clock */
.vtClock-left {
  width: 50%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  background: linear-gradient(45deg, #2c3e50 0%, #34495e 100%);
  border-right: 4px solid #e74c3c;
}

.vtClock-clockContainer {
  position: relative;
  width: 400px;
  height: 400px;
  margin-bottom: 40px;
}

.vtClock-clockFace {
  width: 100%;
  height: 100%;
  border: 8px solid #ecf0f1;
  border-radius: 50%;
  position: relative;
  background: radial-gradient(circle, #34495e 0%, #2c3e50 100%);
  box-shadow: 
    0 0 30px rgba(231, 76, 60, 0.3),
    inset 0 0 30px rgba(0, 0, 0, 0.5);
}

.vtClock-clockFace.warning {
  border-color: #f39c12;
  box-shadow: 
    0 0 40px rgba(243, 156, 18, 0.6),
    inset 0 0 30px rgba(0, 0, 0, 0.5);
}

.vtClock-clockFace.critical {
  border-color: #e74c3c;
  box-shadow: 
    0 0 50px rgba(231, 76, 60, 0.8),
    inset 0 0 30px rgba(0, 0, 0, 0.5);
  animation: pulse 0.5s infinite alternate;
}

@keyframes pulse {
  from { 
    box-shadow: 
      0 0 50px rgba(231, 76, 60, 0.8),
      inset 0 0 30px rgba(0, 0, 0, 0.5);
  }
  to { 
    box-shadow: 
      0 0 70px rgba(231, 76, 60, 1),
      inset 0 0 30px rgba(0, 0, 0, 0.5);
  }
}

.vtClock-timeDisplay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 72px;
  font-weight: 900;
  font-family: 'Orbitron', monospace;
  text-shadow:
    2px 2px 4px rgba(0, 0, 0, 0.8),
    0 0 10px rgba(255, 255, 255, 0.2);
  letter-spacing: -2px;
  transition: all 0.3s ease;
}

.vtClock-timeDisplay.warning {
  color: #f39c12;
}

.vtClock-timeDisplay.critical {
  color: #e74c3c;
  animation: flash 0.5s infinite alternate;
}

@keyframes flash {
  from { opacity: 1; }
  to { opacity: 0.7; }
}

.vtClock-progressRing {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-90deg);
  width: 380px;
  height: 380px;
}

.vtClock-progressCircle {
  fill: none;
  stroke: #3498db;
  stroke-width: 12;
  stroke-linecap: round;
  transition: stroke-dasharray 0.1s ease;
}

.vtClock-progressCircle.warning {
  stroke: #f39c12;
}

.vtClock-progressCircle.critical {
  stroke: #e74c3c;
}

.vtClock-statusLabel {
  font-size: 24px;
  font-weight: bold;
  text-align: center;
  margin-top: 20px;
  padding: 10px 20px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 25px;
  border: 2px solid #3498db;
}

.vtClock-statusLabel.warning {
  border-color: #f39c12;
  color: #f39c12;
}

.vtClock-statusLabel.critical {
  border-color: #e74c3c;
  color: #e74c3c;
}

/* Right Side - Text Fields */
.vtClock-right {
  width: 50%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 60px;
  background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
}

.vtClock-textField {
  margin-bottom: 40px;
  padding: 20px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  border-left: 6px solid #3498db;
}

.vtClock-fieldLabel {
  font-size: 18px;
  font-weight: 700;
  color: #bdc3c7;
  margin-bottom: 10px;
  text-transform: uppercase;
  letter-spacing: 2px;
  font-family: 'Roboto', sans-serif;
  position: relative;
}

.vtClock-fieldLabel::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 30px;
  height: 2px;
  background: linear-gradient(90deg, #3498db, transparent);
}

.vtClock-fieldValue {
  font-size: 32px;
  font-weight: 900;
  color: white;
  text-shadow:
    1px 1px 2px rgba(0, 0, 0, 0.8),
    0 0 5px rgba(255, 255, 255, 0.1);
  line-height: 1.2;
  font-family: 'Roboto', sans-serif;
  transition: all 0.3s ease;
}

.vtClock-fieldValue.large {
  font-size: 42px;
}

.vtClock-fieldValue.small {
  font-size: 24px;
}

/* Responsive adjustments */
@media (max-width: 1600px) {
  .vtClock-clockContainer {
    width: 350px;
    height: 350px;
  }
  
  .vtClock-timeDisplay {
    font-size: 60px;
  }
  
  .vtClock-progressRing {
    width: 330px;
    height: 330px;
  }
}

@media (max-width: 1200px) {
  .vtClock-left,
  .vtClock-right {
    width: 50%;
  }
  
  .vtClock-right {
    padding: 40px;
  }
  
  .vtClock-fieldValue {
    font-size: 28px;
  }
  
  .vtClock-fieldValue.large {
    font-size: 36px;
  }
}

/* Control Panel (Hidden by default, can be shown for testing) */
.vtClock-controls {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  padding: 20px;
  border-radius: 10px;
  display: none; /* Hidden by default for broadcast */
  gap: 10px;
  z-index: 10000;
}

.vtClock-controls.visible {
  display: flex;
}

.vtClock-controlButton {
  padding: 10px 20px;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.3s ease;
}

.vtClock-controlButton:hover {
  background: #2980b9;
  transform: translateY(-2px);
}

.vtClock-controlButton:active {
  transform: translateY(0);
}

.vtClock-controlButton.danger {
  background: #e74c3c;
}

.vtClock-controlButton.danger:hover {
  background: #c0392b;
}

.vtClock-controlButton.success {
  background: #27ae60;
}

.vtClock-controlButton.success:hover {
  background: #229954;
}

/* Broadcast safe area indicators (optional) */
.vtClock-safeArea {
  position: absolute;
  top: 5%;
  left: 5%;
  right: 5%;
  bottom: 5%;
  border: 1px dashed rgba(255, 255, 255, 0.1);
  pointer-events: none;
  display: none; /* Hidden by default */
}

.vtClock-safeArea.visible {
  display: block;
}

/* Additional broadcast enhancements */
.vtClock-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(0deg, transparent 0%, rgba(255,255,255,0.02) 50%, transparent 100%),
    linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.02) 50%, transparent 100%);
  pointer-events: none;
  z-index: 1;
}

/* Ensure content is above the overlay */
.vtClock-left,
.vtClock-right {
  position: relative;
  z-index: 2;
}
