import React, { useState, useEffect, useRef, useCallback } from 'react'
import { render, GsapTimeline, useCaspar, useCasparData } from '@nxtedition/graphics-kit'
import gsap from 'gsap'
import './style.css'

const VTClock = () => {
  const { isPlaying } = useCaspar()
  const {
    duration = 30,
    title = "ITV NEWS",
    programmeName = "NAT BONGS",
    tapeId = "NET31TU25-NN22",
    date = "TUESDAY 29 JULY",
    presenter = "TOM BRADBY",
    takeNumber = "TAKE 2",
    autoStart = false
  } = useCasparData()

  // State management
  const [timeRemaining, setTimeRemaining] = useState(duration)
  const [isCountdownActive, setIsCountdownActive] = useState(false)

  // Refs for GSAP animations
  const handRef = useRef(null)
  const timeDisplayRef = useRef(null)
  const containerRef = useRef(null)

  // Timer ref
  const intervalRef = useRef(null)
  const startTimeRef = useRef(null)

  // Calculate hand rotation angle (semicircle from 0 to 180 degrees)
  const getHandAngle = useCallback((time) => {
    // Map time from duration to 0 onto 180 to 0 degrees
    const progress = time / duration
    return 180 - (progress * 180)
  }, [duration])

  // Start countdown function
  const startCountdown = useCallback(() => {
    if (isCountdownActive) return

    setIsCountdownActive(true)
    setTimeRemaining(duration)
    startTimeRef.current = Date.now()

    intervalRef.current = setInterval(() => {
      const elapsed = (Date.now() - startTimeRef.current) / 1000
      const remaining = Math.max(0, duration - elapsed)

      setTimeRemaining(remaining)

      // Animate the hand rotation
      if (handRef.current) {
        const angle = getHandAngle(remaining)
        gsap.set(handRef.current, { rotation: angle })
      }

      if (remaining <= 0) {
        setIsCountdownActive(false)
        clearInterval(intervalRef.current)
      }
    }, 100) // Update every 100ms for smooth animation
  }, [duration, isCountdownActive, getHandAngle])

  // Stop countdown function
  const stopCountdown = useCallback(() => {
    setIsCountdownActive(false)
    clearInterval(intervalRef.current)
  }, [])

  // Reset countdown function
  const resetCountdown = useCallback(() => {
    setIsCountdownActive(false)
    clearInterval(intervalRef.current)
    setTimeRemaining(duration)

    // Reset hand position
    if (handRef.current) {
      gsap.set(handRef.current, { rotation: 180 })
    }
  }, [duration])

  // Initialize hand position
  useEffect(() => {
    if (handRef.current) {
      gsap.set(handRef.current, { rotation: 180 })
    }
  }, [])

  // Auto-start countdown when component mounts if autoStart is true
  useEffect(() => {
    if (autoStart && isPlaying) {
      const timer = setTimeout(() => {
        startCountdown()
      }, 1000) // Delay to allow entrance animation
      
      return () => clearTimeout(timer)
    }
  }, [autoStart, isPlaying, startCountdown])

  // Cleanup interval on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [])

  // Format time display - just show the seconds
  const formatTime = (seconds) => {
    return Math.ceil(seconds).toString()
  }

  // Generate tick marks and numbers for the semicircle
  const generateTickMarks = () => {
    const ticks = []
    const numbers = []
    const centerX = 250
    const centerY = 250
    const radius = 200

    // Generate tick marks every 5 seconds (30 degrees apart on semicircle)
    for (let i = 0; i <= duration; i += 5) {
      const angle = (i / duration) * 180 // 0 to 180 degrees
      const radians = (angle * Math.PI) / 180

      // Calculate tick mark positions
      const x1 = centerX - Math.cos(radians) * (radius - 15)
      const y1 = centerY - Math.sin(radians) * (radius - 15)
      const x2 = centerX - Math.cos(radians) * radius
      const y2 = centerY - Math.sin(radians) * radius

      ticks.push(
        <line
          key={`tick-${i}`}
          className="vtClock-tick"
          x1={x1}
          y1={y1}
          x2={x2}
          y2={y2}
        />
      )

      // Calculate number positions
      const numX = centerX - Math.cos(radians) * (radius - 35)
      const numY = centerY - Math.sin(radians) * (radius - 35)

      numbers.push(
        <text
          key={`num-${i}`}
          className="vtClock-tickNumber"
          x={numX}
          y={numY}
        >
          {i}
        </text>
      )
    }

    return { ticks, numbers }
  }

  const { ticks, numbers } = generateTickMarks()

  return (
    <GsapTimeline
      hide={!isPlaying}
      onPlay={timeline => {
        // Simple fade in animation
        timeline.from(containerRef.current, {
          opacity: 0,
          duration: 0.5,
          ease: "power2.out"
        })
      }}
      onStop={timeline => {
        // Simple fade out animation
        timeline.to(containerRef.current, {
          opacity: 0,
          duration: 0.5,
          ease: "power2.in"
        })

        // Stop countdown when graphic stops
        stopCountdown()
      }}
    >
      <div ref={containerRef} className="vtClock-container">
        {/* Left Side - Traditional Semicircular Clock */}
        <div className="vtClock-left">
          <div className="vtClock-clockContainer">
            <div className="vtClock-clockFace">
              {/* Semicircular Arc */}
              <svg className="vtClock-arc">
                <path
                  className="vtClock-arcPath"
                  d="M 50 250 A 200 200 0 0 1 450 250"
                />
              </svg>

              {/* Tick Marks and Numbers */}
              <svg className="vtClock-tickMarks">
                {ticks}
                {numbers}
              </svg>

              {/* Clock Hand */}
              <svg ref={handRef} className="vtClock-hand">
                <line
                  className="vtClock-handLine"
                  x1="250"
                  y1="250"
                  x2="250"
                  y2="70"
                />
              </svg>
            </div>

            {/* Center Circle with Time Display */}
            <div className="vtClock-centerCircle">
              <div ref={timeDisplayRef} className="vtClock-timeDisplay">
                {formatTime(timeRemaining)}
              </div>
            </div>
          </div>
        </div>

        {/* Right Side - Traditional Broadcast Text */}
        <div className="vtClock-right">
          <div className="vtClock-title">{title}</div>

          <div className="vtClock-textField">
            <div className="vtClock-fieldValue bold large">{programmeName}</div>
          </div>

          <div className="vtClock-textField">
            <div className="vtClock-fieldValue">{tapeId}</div>
          </div>

          <div className="vtClock-textField">
            <div className="vtClock-fieldValue">{date}</div>
          </div>

          <div className="vtClock-textField">
            <div className="vtClock-fieldValue">{presenter}</div>
          </div>

          <div className="vtClock-textField">
            <div className="vtClock-fieldValue bold">{takeNumber}</div>
          </div>
        </div>

        {/* Control Panel (for testing - hidden in production) */}
        <div className="vtClock-controls">
          <button
            className="vtClock-controlButton"
            onClick={startCountdown}
            disabled={isCountdownActive}
          >
            START
          </button>
          <button
            className="vtClock-controlButton"
            onClick={stopCountdown}
            disabled={!isCountdownActive}
          >
            STOP
          </button>
          <button
            className="vtClock-controlButton"
            onClick={resetCountdown}
          >
            RESET
          </button>
        </div>
      </div>
    </GsapTimeline>
  )
}

render(VTClock)
