import React, { useState, useEffect, useRef, useCallback } from 'react'
import { render, GsapTimeline, useCaspar, useCasparData } from '@nxtedition/graphics-kit'
import gsap from 'gsap'
import './style.css'

const VTClock = () => {
  const { isPlaying } = useCaspar()
  const { 
    duration = 30, 
    programName = "LIVE BROADCAST", 
    segmentTitle = "VT SEGMENT", 
    directorNotes = "READY TO ROLL", 
    operatorName = "OPERATOR",
    autoStart = false 
  } = useCasparData()

  // State management
  const [timeRemaining, setTimeRemaining] = useState(duration)
  const [isCountdownActive, setIsCountdownActive] = useState(false)
  const [countdownStatus, setCountdownStatus] = useState('normal') // normal, warning, critical

  // Refs for GSAP animations
  const clockFaceRef = useRef(null)
  const timeDisplayRef = useRef(null)
  const statusLabelRef = useRef(null)
  const progressCircleRef = useRef(null)
  const containerRef = useRef(null)

  // Timer ref
  const intervalRef = useRef(null)
  const startTimeRef = useRef(null)
  const lastSecondRef = useRef(null)

  // Calculate progress ring properties
  const radius = 184 // Radius for the progress circle
  const circumference = 2 * Math.PI * radius

  // Start countdown function
  const startCountdown = useCallback(() => {
    if (isCountdownActive) return
    
    setIsCountdownActive(true)
    setTimeRemaining(duration)
    startTimeRef.current = Date.now()
    
    intervalRef.current = setInterval(() => {
      const elapsed = (Date.now() - startTimeRef.current) / 1000
      const remaining = Math.max(0, duration - elapsed)
      const currentSecond = Math.floor(remaining)

      // Trigger milestone animations
      if (lastSecondRef.current !== currentSecond) {
        triggerMilestoneAnimation(currentSecond)
        lastSecondRef.current = currentSecond
      }

      setTimeRemaining(remaining)

      if (remaining <= 0) {
        setIsCountdownActive(false)
        clearInterval(intervalRef.current)
        // Final countdown complete animation
        triggerCountdownCompleteAnimation()
      }
    }, 100) // Update every 100ms for smooth animation
  }, [duration, isCountdownActive])

  // Stop countdown function
  const stopCountdown = useCallback(() => {
    setIsCountdownActive(false)
    clearInterval(intervalRef.current)
    setTimeRemaining(duration)
  }, [duration])

  // Reset countdown function
  const resetCountdown = useCallback(() => {
    setIsCountdownActive(false)
    clearInterval(intervalRef.current)
    setTimeRemaining(duration)
  }, [duration])

  // Milestone animation function
  const triggerMilestoneAnimation = useCallback((second) => {
    const tl = gsap.timeline()

    // Special animations for key moments
    if (second === 10) {
      // 10 second warning
      tl.to(timeDisplayRef.current, {
        scale: 1.2,
        duration: 0.2,
        ease: "power2.out"
      })
      .to(timeDisplayRef.current, {
        scale: 1,
        duration: 0.2,
        ease: "power2.out"
      })
      .to(containerRef.current, {
        boxShadow: "inset 0 0 100px rgba(243, 156, 18, 0.3)",
        duration: 0.5
      }, 0)

    } else if (second === 5) {
      // 5 second critical warning
      tl.to(timeDisplayRef.current, {
        scale: 1.3,
        duration: 0.15,
        ease: "power2.out"
      })
      .to(timeDisplayRef.current, {
        scale: 1,
        duration: 0.15,
        ease: "power2.out"
      })
      .to(containerRef.current, {
        boxShadow: "inset 0 0 150px rgba(231, 76, 60, 0.4)",
        duration: 0.5
      }, 0)

    } else if (second <= 3 && second > 0) {
      // Final 3 seconds - intense animation
      tl.to(timeDisplayRef.current, {
        scale: 1.4,
        duration: 0.1,
        ease: "power2.out"
      })
      .to(timeDisplayRef.current, {
        scale: 1,
        duration: 0.1,
        ease: "power2.out"
      })
      .to(clockFaceRef.current, {
        rotation: "+=5",
        duration: 0.1,
        ease: "power2.out"
      }, 0)
      .to(clockFaceRef.current, {
        rotation: "-=5",
        duration: 0.1,
        ease: "power2.out"
      })
    }
  }, [])

  // Countdown complete animation
  const triggerCountdownCompleteAnimation = useCallback(() => {
    const tl = gsap.timeline()

    tl.to(timeDisplayRef.current, {
      scale: 1.5,
      color: "#e74c3c",
      duration: 0.3,
      ease: "power2.out"
    })
    .to(clockFaceRef.current, {
      scale: 1.1,
      duration: 0.3,
      ease: "power2.out"
    }, 0)
    .to(containerRef.current, {
      boxShadow: "inset 0 0 200px rgba(231, 76, 60, 0.6)",
      duration: 0.5
    }, 0)
    .to([timeDisplayRef.current, clockFaceRef.current], {
      scale: 1,
      duration: 0.5,
      ease: "elastic.out(1, 0.3)"
    }, "+=0.2")
    .to(containerRef.current, {
      boxShadow: "none",
      duration: 1
    }, "-=0.3")
  }, [])

  // Update countdown status based on time remaining with animations
  useEffect(() => {
    const percentage = (timeRemaining / duration) * 100
    let newStatus = 'normal'

    if (percentage <= 10) {
      newStatus = 'critical'
    } else if (percentage <= 25) {
      newStatus = 'warning'
    }

    // Animate status changes
    if (newStatus !== countdownStatus) {
      const tl = gsap.timeline()

      if (newStatus === 'critical') {
        // Critical animation - intense pulsing and shaking
        tl.to(clockFaceRef.current, {
          scale: 1.05,
          duration: 0.2,
          ease: "power2.out"
        })
        .to(clockFaceRef.current, {
          scale: 1,
          duration: 0.2,
          ease: "power2.out"
        })
        .to(timeDisplayRef.current, {
          textShadow: "0 0 20px #e74c3c, 2px 2px 4px rgba(0,0,0,0.8)",
          duration: 0.3
        }, 0)

        // Add subtle shake animation
        gsap.to(clockFaceRef.current, {
          x: "+=2",
          duration: 0.1,
          repeat: -1,
          yoyo: true,
          ease: "power2.inOut"
        })

      } else if (newStatus === 'warning') {
        // Warning animation - gentle pulse
        tl.to(clockFaceRef.current, {
          scale: 1.02,
          duration: 0.3,
          ease: "power2.out"
        })
        .to(clockFaceRef.current, {
          scale: 1,
          duration: 0.3,
          ease: "power2.out"
        })
        .to(timeDisplayRef.current, {
          textShadow: "0 0 15px #f39c12, 2px 2px 4px rgba(0,0,0,0.8)",
          duration: 0.3
        }, 0)

        // Stop any existing shake animation
        gsap.killTweensOf(clockFaceRef.current)
        gsap.set(clockFaceRef.current, { x: 0 })

      } else {
        // Normal state - reset animations
        gsap.killTweensOf(clockFaceRef.current)
        tl.to(clockFaceRef.current, {
          scale: 1,
          x: 0,
          duration: 0.3,
          ease: "power2.out"
        })
        .to(timeDisplayRef.current, {
          textShadow: "2px 2px 4px rgba(0,0,0,0.8)",
          duration: 0.3
        }, 0)
      }

      // Animate status label change
      tl.to(statusLabelRef.current, {
        scale: 1.1,
        duration: 0.2,
        ease: "power2.out"
      }, 0)
      .to(statusLabelRef.current, {
        scale: 1,
        duration: 0.2,
        ease: "power2.out"
      })
    }

    setCountdownStatus(newStatus)
  }, [timeRemaining, duration, countdownStatus])

  // Auto-start countdown when component mounts if autoStart is true
  useEffect(() => {
    if (autoStart && isPlaying) {
      const timer = setTimeout(() => {
        startCountdown()
      }, 1000) // Delay to allow entrance animation
      
      return () => clearTimeout(timer)
    }
  }, [autoStart, isPlaying, startCountdown])

  // Cleanup interval on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [])

  // Format time display
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    const centiseconds = Math.floor((seconds % 1) * 100)
    
    if (mins > 0) {
      return `${mins}:${secs.toString().padStart(2, '0')}`
    } else {
      return `${secs}.${centiseconds.toString().padStart(2, '0')}`
    }
  }

  // Calculate stroke dash array for progress ring
  const progressOffset = circumference - (timeRemaining / duration) * circumference

  // Status label text
  const getStatusLabel = () => {
    if (!isCountdownActive) return 'READY'
    if (countdownStatus === 'critical') return 'CRITICAL'
    if (countdownStatus === 'warning') return 'WARNING'
    return 'ROLLING'
  }

  return (
    <GsapTimeline
      hide={!isPlaying}
      onPlay={timeline => {
        // Entrance animation
        timeline
          .from(containerRef.current, { 
            opacity: 0, 
            scale: 0.8, 
            duration: 1, 
            ease: "back.out(1.7)" 
          })
          .from('.vtClock-left', { 
            x: -100, 
            opacity: 0, 
            duration: 0.8, 
            ease: "power2.out" 
          }, "-=0.5")
          .from('.vtClock-right', { 
            x: 100, 
            opacity: 0, 
            duration: 0.8, 
            ease: "power2.out" 
          }, "-=0.8")
          .from('.vtClock-textField', { 
            y: 50, 
            opacity: 0, 
            duration: 0.6, 
            stagger: 0.1, 
            ease: "power2.out" 
          }, "-=0.4")
      }}
      onStop={timeline => {
        // Exit animation
        timeline
          .to('.vtClock-textField', { 
            y: -50, 
            opacity: 0, 
            duration: 0.4, 
            stagger: 0.05 
          })
          .to('.vtClock-left, .vtClock-right', { 
            scale: 0.9, 
            opacity: 0, 
            duration: 0.6, 
            ease: "power2.in" 
          }, "-=0.2")
          .to(containerRef.current, { 
            opacity: 0, 
            scale: 0.8, 
            duration: 0.8, 
            ease: "power2.in" 
          }, "-=0.4")
        
        // Stop countdown when graphic stops
        stopCountdown()
      }}
    >
      <div ref={containerRef} className="vtClock-container">
        {/* Left Side - Countdown Clock */}
        <div className="vtClock-left">
          <div className="vtClock-clockContainer">
            <div 
              ref={clockFaceRef} 
              className={`vtClock-clockFace ${countdownStatus}`}
            >
              {/* Progress Ring */}
              <svg className="vtClock-progressRing">
                <circle
                  ref={progressCircleRef}
                  className={`vtClock-progressCircle ${countdownStatus}`}
                  cx="190"
                  cy="190"
                  r={radius}
                  strokeDasharray={circumference}
                  strokeDashoffset={progressOffset}
                />
              </svg>
              
              {/* Time Display */}
              <div 
                ref={timeDisplayRef} 
                className={`vtClock-timeDisplay ${countdownStatus}`}
              >
                {formatTime(timeRemaining)}
              </div>
            </div>
          </div>
          
          {/* Status Label */}
          <div 
            ref={statusLabelRef} 
            className={`vtClock-statusLabel ${countdownStatus}`}
          >
            {getStatusLabel()}
          </div>
        </div>

        {/* Right Side - Text Fields */}
        <div className="vtClock-right">
          <div className="vtClock-textField">
            <div className="vtClock-fieldLabel">Program</div>
            <div className="vtClock-fieldValue large">{programName}</div>
          </div>

          <div className="vtClock-textField">
            <div className="vtClock-fieldLabel">Segment</div>
            <div className="vtClock-fieldValue">{segmentTitle}</div>
          </div>

          <div className="vtClock-textField">
            <div className="vtClock-fieldLabel">Director Notes</div>
            <div className="vtClock-fieldValue small">{directorNotes}</div>
          </div>

          <div className="vtClock-textField">
            <div className="vtClock-fieldLabel">Operator</div>
            <div className="vtClock-fieldValue small">{operatorName}</div>
          </div>
        </div>

        {/* Control Panel (for testing - hidden in production) */}
        <div className="vtClock-controls">
          <button
            className="vtClock-controlButton success"
            onClick={startCountdown}
            disabled={isCountdownActive}
          >
            START
          </button>
          <button
            className="vtClock-controlButton danger"
            onClick={stopCountdown}
            disabled={!isCountdownActive}
          >
            STOP
          </button>
          <button
            className="vtClock-controlButton"
            onClick={resetCountdown}
          >
            RESET
          </button>
        </div>

        {/* Broadcast Safe Area Guide (optional) */}
        <div className="vtClock-safeArea"></div>
      </div>
    </GsapTimeline>
  )
}

render(VTClock)
