# VT Clock - Traditional Broadcast Video Tape Countdown Interface

A traditional broadcast-style VT clock interface designed for CasparCG, replicating the classic BBC/ITV countdown clock aesthetic with a semicircular analog clock and professional broadcast text layout.

## Features

### Traditional Semicircular Clock (Left Side)
- **Semicircular analog countdown** with white arc on black background
- **Rotating white hand** that moves clockwise from 30 seconds to 0
- **Tick marks and numerals** at 5-second intervals (0, 5, 10, 15, 20, 25, 30)
- **Center circle** with bold white countdown number display
- **Smooth hand animation** with accurate timing (100ms update intervals)
- **Customizable duration** (default 30 seconds, editable via manifest)

### Professional Broadcast Text (Right Side)
- **Title**: Main program identifier (e.g., "ITV NEWS")
- **Programme Name**: Bold program name (e.g., "NAT BONGS")
- **Tape ID**: Technical identifier (e.g., "NET31TU25-NN22")
- **Date**: Broadcast date (e.g., "TUESDAY 29 JULY")
- **Presenter**: Presenter name (e.g., "TOM BRADBY")
- **Take Number**: Bold take identifier (e.g., "TAKE 2")
- All text fields are **fully customizable** via CasparCG data
- **Clean sans-serif Arial font** for broadcast clarity

### GSAP Animations
- **Simple fade in/out**: Professional broadcast-style entrance and exit
- **Smooth hand rotation**: Continuous clockwise movement of the countdown hand
- **Accurate timing**: Real-time synchronization between hand position and digital display

## Technical Specifications

### CasparCG Integration
- **Framework**: React with NXT Edition Graphics Kit
- **Animation Library**: GSAP (GreenSock Animation Platform)
- **Resolution**: 1920x1080 (Full HD)
- **Format**: HTML5 template for CasparCG Server

### Customizable Parameters
Configure via CasparCG or the manifest schema:

```json
{
  "duration": 30,              // Countdown duration in seconds
  "title": "ITV NEWS",         // Main program title
  "programmeName": "NAT BONGS", // Programme name (bold)
  "tapeId": "NET31TU25-NN22",  // Technical tape identifier
  "date": "TUESDAY 29 JULY",   // Broadcast date
  "presenter": "TOM BRADBY",   // Presenter name
  "takeNumber": "TAKE 2",      // Take number (bold)
  "autoStart": false           // Auto-start countdown on play
}
```

### Traditional Broadcast Design
- **Primary target**: 1920x1080 broadcast resolution
- **Pure black background** for professional broadcast look
- **White-on-black styling** matching traditional VT clocks
- **Clean Arial typography** for maximum broadcast clarity
- **Responsive scaling** for different display sizes

## Usage Instructions

### In CasparCG
1. Load the template: `CG 1-1 ADD 0 vtClock 1`
2. Set parameters: `CG 1-1 UPDATE 0 "<templateData><componentData><data id=\"duration\" value=\"30\"/><data id=\"programName\" value=\"YOUR PROGRAM\"/>...</componentData></templateData>"`
3. Play the graphic: `CG 1-1 PLAY 0`
4. Stop the graphic: `CG 1-1 STOP 0`

### Control Options
- **Manual Control**: Use CasparCG commands to start/stop
- **Auto-Start**: Set `autoStart: true` to begin countdown automatically
- **Duration**: Adjust countdown length from 1-999 seconds

## Visual Design

### Traditional Broadcast Aesthetic
- **Black background** throughout the entire interface
- **White elements** for maximum contrast and clarity
- **Semicircular clock face** with clean white arc
- **Rotating hand** that moves smoothly from 30 to 0
- **Bold center circle** displaying current countdown number
- **Professional typography** using Arial sans-serif font
- **Clean text layout** with proper spacing and hierarchy

## File Structure
```
templates/vtClock/
├── index.jsx          # Main React component
├── style.css          # Broadcast-quality styling
├── manifest.json      # CasparCG template configuration
├── index.html         # HTML wrapper
└── README.md          # This documentation
```

## Browser Compatibility
- Modern browsers with ES6+ support
- Optimized for CasparCG's CEF (Chromium Embedded Framework)
- Hardware acceleration recommended for smooth animations

## Customization Notes
- **Typography**: Uses Arial font family for authentic broadcast look
- **Colors**: Pure black background with white elements (easily customizable via CSS)
- **Clock Design**: Semicircular arc and hand can be styled via SVG properties
- **Text Layout**: Vertically stacked text fields with proper spacing
- **Timing**: Hand rotation and digital display synchronized via GSAP

## Production Considerations
- **Broadcast Ready**: Designed for professional television production
- **Control Panel**: Hidden by default, can be enabled for testing
- **High Contrast**: White-on-black design meets broadcast standards
- **Performance**: Optimized for real-time graphics rendering in CasparCG
- **Traditional Style**: Matches classic BBC/ITV VT clock aesthetics
