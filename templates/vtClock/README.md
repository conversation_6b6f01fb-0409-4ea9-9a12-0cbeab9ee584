# VT Clock - Broadcast Video Tape Countdown Interface

A professional broadcast-quality countdown clock interface designed for CasparCG, featuring a split-screen layout with an analogue countdown clock and customizable text fields.

## Features

### Countdown Clock (Left Side)
- **Analogue 30-second countdown** (duration is editable via manifest)
- **Large, broadcast-readable display** using Orbitron monospace font
- **Visual status indicators:**
  - Normal: Blue progress ring and white text
  - Warning (≤25%): Orange/yellow indicators with gentle pulse animation
  - Critical (≤10%): Red indicators with intense pulsing and shake effects
- **Milestone animations:**
  - 10-second warning: Scale animation and container glow
  - 5-second critical: Enhanced scale animation and red glow
  - Final 3 seconds: Intense scaling and rotation effects
  - Countdown complete: Final celebration animation
- **Smooth progress ring** showing remaining time visually
- **Accurate timing** with 100ms update intervals

### Text Fields (Right Side)
- **Program Name**: Large display for show/program identification
- **Segment Title**: Medium display for current segment
- **Director Notes**: Smaller display for production notes
- **Operator Name**: Operator identification field
- All fields are **fully customizable** via CasparCG data

### GSAP Animations
- **Professional entrance animation**: Scale and slide effects with staggered text field reveals
- **Smooth exit animation**: Coordinated fade and scale transitions
- **Real-time status transitions**: Animated changes between normal/warning/critical states
- **Milestone celebrations**: Special animations at key countdown moments
- **Elastic and bounce effects** for enhanced visual appeal

## Technical Specifications

### CasparCG Integration
- **Framework**: React with NXT Edition Graphics Kit
- **Animation Library**: GSAP (GreenSock Animation Platform)
- **Resolution**: 1920x1080 (Full HD)
- **Format**: HTML5 template for CasparCG Server

### Customizable Parameters
Configure via CasparCG or the manifest schema:

```json
{
  "duration": 30,              // Countdown duration in seconds
  "programName": "LIVE BROADCAST",
  "segmentTitle": "VT SEGMENT",
  "directorNotes": "READY TO ROLL",
  "operatorName": "OPERATOR",
  "autoStart": false           // Auto-start countdown on play
}
```

### Responsive Design
- **Primary target**: 1920x1080 broadcast resolution
- **Responsive breakpoints** for 1600px and 1200px widths
- **Broadcast-safe styling** with optional safe area guides
- **Professional color scheme** with gradient backgrounds

## Usage Instructions

### In CasparCG
1. Load the template: `CG 1-1 ADD 0 vtClock 1`
2. Set parameters: `CG 1-1 UPDATE 0 "<templateData><componentData><data id=\"duration\" value=\"30\"/><data id=\"programName\" value=\"YOUR PROGRAM\"/>...</componentData></templateData>"`
3. Play the graphic: `CG 1-1 PLAY 0`
4. Stop the graphic: `CG 1-1 STOP 0`

### Control Options
- **Manual Control**: Use CasparCG commands to start/stop
- **Auto-Start**: Set `autoStart: true` to begin countdown automatically
- **Duration**: Adjust countdown length from 1-999 seconds

## Visual States

### Normal State (>25% remaining)
- Blue progress ring and accents
- White countdown text
- Steady, professional appearance

### Warning State (10-25% remaining)
- Orange/yellow indicators
- Gentle pulsing animation
- Enhanced visibility

### Critical State (<10% remaining)
- Red indicators throughout
- Intense pulsing and shake effects
- Flashing countdown text
- Maximum urgency visual cues

## File Structure
```
templates/vtClock/
├── index.jsx          # Main React component
├── style.css          # Broadcast-quality styling
├── manifest.json      # CasparCG template configuration
├── index.html         # HTML wrapper
└── README.md          # This documentation
```

## Browser Compatibility
- Modern browsers with ES6+ support
- Optimized for CasparCG's CEF (Chromium Embedded Framework)
- Hardware acceleration recommended for smooth animations

## Customization Notes
- Fonts: Uses Google Fonts (Orbitron for countdown, Roboto for text)
- Colors: Easily customizable via CSS variables
- Animations: GSAP timelines can be modified for different effects
- Layout: Responsive grid system allows for easy repositioning

## Production Considerations
- Control panel is hidden by default for broadcast use
- Safe area guides available for broadcast compliance
- High contrast ratios for broadcast standards
- Optimized performance for real-time graphics rendering
