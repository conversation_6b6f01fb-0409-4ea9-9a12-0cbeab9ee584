/* Graphic container for clock and score */
.graphic-container {
  position: fixed;
  top: 50px;
  left: 96px;
  display: flex;
  align-items: center;
  gap: 12px;
  height: 50px;
  will-change: transform; /* Optimize for animations */
  backface-visibility: hidden; /* Prevent flickering */
  transform: translateZ(0); /* Force hardware acceleration */
}

/* Clock container styles */
.clock-container {
  position: relative;
  background-color: white;
  color: black;
  font-family: 'Arial', sans-serif;
  font-size: 28px;
  font-weight: bold;
  padding: 0 15px;
  border-radius: 6px;
  box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-width: 150px;
  will-change: transform;
  backface-visibility: hidden;
}

.clock-text {
  display: block;
  line-height: 1;
  letter-spacing: 0.5px;
}

/* Extra time container styles */
.extra-time-container {
  position: absolute;
  top: calc(100% + 12px); /* Positioned below the clock with better spacing */
  left: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  will-change: transform, opacity;
  backface-visibility: hidden;
  transform: translateZ(0);
  z-index: 10;
}

/* Extra time clock styles */
.extra-time-clock {
  background-color: white;
  color: black;
  font-family: 'Arial', sans-serif;
  font-size: 22px;
  font-weight: bold;
  padding: 6px 12px;
  border-radius: 4px;
  box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.1);
  line-height: 1;
  letter-spacing: 0.5px;
  will-change: transform;
  backface-visibility: hidden;
}

/* Extra time plus styles */
.extra-time-plus {
  background-color: #001f3f;
  color: white;
  font-family: 'Arial', sans-serif;
  font-size: 22px;
  font-weight: bold;
  padding: 6px 12px;
  border-radius: 4px;
  box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.2);
  line-height: 1;
  letter-spacing: 0.5px;
  will-change: transform;
  backface-visibility: hidden;
}

/* Score container styles */
.score-container {
  display: flex;
  align-items: center;
  background-color: #001f3f;
  color: white;
  font-family: 'Arial', sans-serif;
  font-size: 28px;
  font-weight: bold;
  border-radius: 6px;
  box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.15);
  height: 100%;
  overflow: hidden;
  will-change: transform;
  backface-visibility: hidden;
}

/* Team color stripe styles */
.team-color {
  width: 12px;
  height: 100%;
  flex-shrink: 0;
  will-change: transform;
  backface-visibility: hidden;
}

.home-color {
  border-radius: 6px 0 0 6px;
}

.away-color {
  border-radius: 0 6px 6px 0;
}

/* Team name styles */
.team-name {
  margin: 0 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
  line-height: 1;
  letter-spacing: 0.5px;
  will-change: transform;
  backface-visibility: hidden;
}

.home-team {
  text-align: right;
}

.away-team {
  text-align: left;
}

/* Score styles */
.team-score {
  margin: 0 8px;
  font-weight: 900;
  min-width: 24px;
  text-align: center;
  line-height: 1;
  letter-spacing: 0.5px;
  will-change: transform;
  backface-visibility: hidden;
}

/* Divider style */
.divider {
  margin: 0 8px;
  font-size: 24px;
  font-weight: bold;
  color: white;
  line-height: 1;
  will-change: transform;
  backface-visibility: hidden;
}

/* Performance optimizations */
* {
  box-sizing: border-box;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .graphic-container,
  .extra-time-container,
  .clock-container,
  .score-container {
    transition: none !important;
    animation: none !important;
  }
}
